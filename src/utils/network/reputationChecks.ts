/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import type { Message } from 'discord.js';
import type { User as DbUser } from '#src/generated/prisma/client/client.js';
import type HubManager from '#src/managers/HubManager.js';
import { HubReputationService } from '#src/services/HubReputationService.js';
import { getEmoji } from '#src/utils/EmojiUtils.js';
import Logger from '#src/utils/Logger.js';

export interface ReputationCheckResult {
  passed: boolean;
  reason?: string;
  requiredRP?: number;
  currentRP?: number;
}

export interface ReputationCheckOptions {
  userData: DbUser;
  hub: HubManager;
  attachmentURL?: string | null;
}

/**
 * Checks if user has sufficient reputation for image uploads
 */
export async function checkImageUploadReputation(
  message: Message<true>,
  opts: ReputationCheckOptions,
): Promise<ReputationCheckResult> {
  // Only check if message has image attachments
  const hasImages = message.attachments.some((attachment) =>
    attachment.contentType?.startsWith('image/') &&
    !attachment.contentType.includes('gif'),
  ) || (opts.attachmentURL && !opts.attachmentURL.includes('.gif'));

  if (!hasImages) {
    return { passed: true };
  }

  const reputationService = new HubReputationService();
  const [currentRP, requirements] = await Promise.all([
    reputationService.getUserReputation(message.author.id, opts.hub.id),
    reputationService.getHubReputationRequirements(opts.hub.id),
  ]);

  if (currentRP < requirements.imageUpload) {
    return {
      passed: false,
      reason: `${getEmoji('x_icon', message.client)} You need **${requirements.imageUpload} RP** to upload images in this hub. You currently have **${currentRP} RP**. Send more messages to earn reputation!`,
      requiredRP: requirements.imageUpload,
      currentRP,
    };
  }

  return { passed: true };
}

/**
 * Checks if user has sufficient reputation for GIF uploads
 */
export async function checkGifUploadReputation(
  message: Message<true>,
  opts: ReputationCheckOptions,
): Promise<ReputationCheckResult> {
  // Check for GIF attachments or GIF URLs
  const hasGifs = message.attachments.some((attachment) =>
    attachment.contentType?.includes('gif'),
  ) || (opts.attachmentURL && opts.attachmentURL.includes('.gif'));

  if (!hasGifs) {
    return { passed: true };
  }

  const reputationService = new HubReputationService();
  const [currentRP, requirements] = await Promise.all([
    reputationService.getUserReputation(message.author.id, opts.hub.id),
    reputationService.getHubReputationRequirements(opts.hub.id),
  ]);

  if (currentRP < requirements.gifUpload) {
    return {
      passed: false,
      reason: `${getEmoji('x_icon', message.client)} You need **${requirements.gifUpload} RP** to upload GIFs in this hub. You currently have **${currentRP} RP**. Keep participating to earn more reputation!`,
      requiredRP: requirements.gifUpload,
      currentRP,
    };
  }

  return { passed: true };
}

/**
 * Checks if user has sufficient reputation for video uploads
 */
export async function checkVideoUploadReputation(
  message: Message<true>,
  opts: ReputationCheckOptions,
): Promise<ReputationCheckResult> {
  // Check for video attachments
  const hasVideos = message.attachments.some((attachment) =>
    attachment.contentType?.startsWith('video/'),
  );

  if (!hasVideos) {
    return { passed: true };
  }

  const reputationService = new HubReputationService();
  const [currentRP, requirements] = await Promise.all([
    reputationService.getUserReputation(message.author.id, opts.hub.id),
    reputationService.getHubReputationRequirements(opts.hub.id),
  ]);

  if (currentRP < requirements.videoUpload) {
    return {
      passed: false,
      reason: `${getEmoji('x_icon', message.client)} You need **${requirements.videoUpload} RP** to upload videos in this hub. You currently have **${currentRP} RP**. Continue being active to build your reputation!`,
      requiredRP: requirements.videoUpload,
      currentRP,
    };
  }

  return { passed: true };
}

/**
 * Checks if user has sufficient reputation for stickers
 */
export async function checkStickerReputation(
  message: Message<true>,
  opts: ReputationCheckOptions,
): Promise<ReputationCheckResult> {
  if (message.stickers.size === 0) {
    return { passed: true };
  }

  const reputationService = new HubReputationService();
  const [currentRP, requirements] = await Promise.all([
    reputationService.getUserReputation(message.author.id, opts.hub.id),
    reputationService.getHubReputationRequirements(opts.hub.id),
  ]);

  if (currentRP < requirements.sticker) {
    return {
      passed: false,
      reason: `${getEmoji('x_icon', message.client)} You need **${requirements.sticker} RP** to send stickers in this hub. You currently have **${currentRP} RP**. Send more messages to earn reputation!`,
      requiredRP: requirements.sticker,
      currentRP,
    };
  }

  return { passed: true };
}

/**
 * Checks if user has sufficient reputation for long messages
 */
export async function checkLongMessageReputation(
  message: Message<true>,
  opts: ReputationCheckOptions,
): Promise<ReputationCheckResult> {
  // Consider messages over 500 characters as "long"
  const LONG_MESSAGE_THRESHOLD = 500;

  if (message.content.length <= LONG_MESSAGE_THRESHOLD) {
    return { passed: true };
  }

  const reputationService = new HubReputationService();
  const [currentRP, requirements] = await Promise.all([
    reputationService.getUserReputation(message.author.id, opts.hub.id),
    reputationService.getHubReputationRequirements(opts.hub.id),
  ]);

  if (currentRP < requirements.longMessage) {
    return {
      passed: false,
      reason: `${getEmoji('x_icon', message.client)} You need **${requirements.longMessage} RP** to send long messages in this hub. You currently have **${currentRP} RP**. Build your reputation with shorter messages first!`,
      requiredRP: requirements.longMessage,
      currentRP,
    };
  }

  return { passed: true };
}

/**
 * Checks if user has sufficient reputation for links and formatting (rich content)
 */
export async function checkEmbedReputation(
  message: Message<true>,
  opts: ReputationCheckOptions,
): Promise<ReputationCheckResult> {
  // Check if message contains links, code blocks, spoilers, or other formatting
  const linkMatches = message.content.match(/https?:\/\/[^\s]+/g);
  const hasRichContent = message.embeds.length > 0 ||
    message.content.includes('```') || // Code blocks
    message.content.includes('||') || // Spoilers
    (linkMatches && linkMatches.length > 1); // Multiple links

  if (!hasRichContent) {
    return { passed: true };
  }

  const reputationService = new HubReputationService();
  const [currentRP, requirements] = await Promise.all([
    reputationService.getUserReputation(message.author.id, opts.hub.id),
    reputationService.getHubReputationRequirements(opts.hub.id),
  ]);

  if (currentRP < requirements.embed) {
    return {
      passed: false,
      reason: `${getEmoji('x_icon', message.client)} You need **${requirements.embed} RP** to send links and formatted content in this hub. You currently have **${currentRP} RP**. Participate more to unlock this feature!`,
      requiredRP: requirements.embed,
      currentRP,
    };
  }

  return { passed: true };
}

/**
 * Awards reputation for a successful message
 */
export async function awardMessageReputation(
  message: Message<true>,
  hubId: string,
): Promise<void> {
  try {
    const reputationService = new HubReputationService();
    const newReputation = await reputationService.awardMessageReputation(
      message.author.id,
      hubId,
      message.content.length,
    );

    Logger.debug(
      `Awarded reputation to user ${message.author.id} in hub ${hubId}. New total: ${newReputation} RP`,
    );
  }
  catch (error) {
    Logger.error('Error awarding message reputation:', error);
  }
}

/**
 * Applies reputation penalty for violations
 */
export async function applyReputationPenalty(
  userId: string,
  hubId: string,
  penaltyType: 'AUTO_BLACKLIST' | 'MANUAL_BLACKLIST_TEMP' | 'MANUAL_BLACKLIST_PERM' | 'WARNING',
  moderatorId?: string,
): Promise<void> {
  try {
    const reputationService = new HubReputationService();
    const newReputation = await reputationService.applyPenalty(
      userId,
      hubId,
      penaltyType,
      moderatorId,
    );

    Logger.debug(
      `Applied ${penaltyType} penalty to user ${userId} in hub ${hubId}. New total: ${newReputation} RP`,
    );
  }
  catch (error) {
    Logger.error('Error applying reputation penalty:', error);
  }
}

/**
 * Applies reputation penalty for manual blacklists with automatic temp/perm detection
 */
export async function applyManualBlacklistPenalty(
  userId: string,
  hubId: string,
  expiresAt: Date | null,
  moderatorId?: string,
): Promise<void> {
  // Determine if blacklist is temporary or permanent based on expiration
  const penaltyType = expiresAt ? 'MANUAL_BLACKLIST_TEMP' : 'MANUAL_BLACKLIST_PERM';
  await applyReputationPenalty(userId, hubId, penaltyType, moderatorId);
}

/**
 * All reputation checks that should be run for hub messages
 */
export const reputationChecks = [
  checkImageUploadReputation,
  checkGifUploadReputation,
  checkVideoUploadReputation,
  checkStickerReputation,
  checkLongMessageReputation,
  checkEmbedReputation,
] as const;
