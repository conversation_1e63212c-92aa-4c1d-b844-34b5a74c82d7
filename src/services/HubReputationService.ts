/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { CacheManager } from '#src/managers/CacheManager.js';
import db from '#src/utils/Db.js';
import { getRedis } from '#src/utils/Redis.js';
import { RedisKeys } from '#src/utils/Constants.js';
import Logger from '#src/utils/Logger.js';
import type { HubReputation, HubReputationSettings } from '#src/generated/prisma/client/client.js';
import type { Snowflake } from 'discord.js';

export interface ReputationChangeOptions {
  reason: string;
  moderatorId?: string;
  automatic?: boolean;
}

export interface ReputationRequirements {
  imageUpload: number;
  gifUpload: number;
  videoUpload: number;
  sticker: number;
  longMessage: number;
  embed: number;
}

/**
 * Service for managing hub-specific reputation system
 * Optimized for InterChat's 5GB RAM constraint across 9 shards
 */
export class HubReputationService {
  private readonly cacheManager: CacheManager;
  private readonly settingsCacheManager: CacheManager;

  // Cache TTL constants (in seconds)
  private static readonly REPUTATION_CACHE_TTL = 10 * 60; // 10 minutes
  private static readonly SETTINGS_CACHE_TTL = 30 * 60; // 30 minutes

  // Reputation change constants
  private static readonly PENALTIES = {
    AUTO_BLACKLIST: -0.2,
    MANUAL_BLACKLIST_TEMP: -3.0,
    MANUAL_BLACKLIST_PERM: -10.0,
    WARNING: -1.0,
  } as const;

  constructor() {
    this.cacheManager = new CacheManager(getRedis(), {
      prefix: RedisKeys.HubReputation,
      expirationMs: HubReputationService.REPUTATION_CACHE_TTL * 1000,
    });

    this.settingsCacheManager = new CacheManager(getRedis(), {
      prefix: `${RedisKeys.HubReputation}:settings`,
      expirationMs: HubReputationService.SETTINGS_CACHE_TTL * 1000,
    });
  }

  /**
   * Awards reputation points for a message sent in a hub
   * @param userId - The user who sent the message
   * @param hubId - The hub where the message was sent
   * @param messageLength - Length of the message (affects reward calculation)
   * @returns The new reputation value
   */
  async awardMessageReputation(
    userId: Snowflake,
    hubId: string,
    messageLength: number = 0,
  ): Promise<number> {
    const settings = await this.getHubReputationSettings(hubId);

    // Calculate reward based on message length (longer messages get slightly more RP)
    const baseReward = settings.messageRewardMin;
    const lengthBonus = Math.min(
      (messageLength / 100) * 0.1, // 0.1 RP per 100 characters
      settings.messageRewardMax - settings.messageRewardMin,
    );
    const rewardAmount = baseReward + lengthBonus;

    return await this.changeReputation(userId, hubId, rewardAmount, {
      reason: 'Message sent',
      automatic: true,
    });
  }

  /**
   * Applies reputation penalty for violations
   * @param userId - The user receiving the penalty
   * @param hubId - The hub where the violation occurred
   * @param penaltyType - Type of penalty to apply
   * @param moderatorId - ID of the moderator applying the penalty (if manual)
   * @returns The new reputation value
   */
  async applyPenalty(
    userId: Snowflake,
    hubId: string,
    penaltyType: keyof typeof HubReputationService.PENALTIES,
    moderatorId?: string,
  ): Promise<number> {
    const penaltyAmount = HubReputationService.PENALTIES[penaltyType];

    return await this.changeReputation(userId, hubId, penaltyAmount, {
      reason: `Penalty: ${penaltyType.toLowerCase().replace('_', ' ')}`,
      moderatorId,
      automatic: !moderatorId,
    });
  }

  /**
   * Changes a user's reputation in a specific hub
   * @param userId - The user whose reputation to change
   * @param hubId - The hub where the reputation change occurs
   * @param amount - Amount to change (positive or negative)
   * @param options - Additional options for the change
   * @returns The new reputation value
   */
  async changeReputation(
    userId: Snowflake,
    hubId: string,
    amount: number,
    options: ReputationChangeOptions,
  ): Promise<number> {
    const startTime = performance.now();

    try {
      // Update reputation in database with upsert
      const updatedReputation = await db.hubReputation.upsert({
        where: {
          userId_hubId: { userId, hubId },
        },
        create: {
          userId,
          hubId,
          reputation: Math.max(0, amount), // Ensure reputation doesn't go below 0
        },
        update: {
          reputation: {
            increment: amount,
          },
          lastUpdated: new Date(),
        },
        select: {
          reputation: true,
        },
      });

      // Ensure reputation doesn't go below 0
      if (updatedReputation.reputation < 0) {
        await db.hubReputation.update({
          where: {
            userId_hubId: { userId, hubId },
          },
          data: {
            reputation: 0,
          },
        });
        updatedReputation.reputation = 0;
      }

      // Invalidate cache for this user-hub combination
      await this.invalidateUserReputationCache(userId, hubId);

      // Log the reputation change for audit purposes
      Logger.debug(
        `Reputation change: User ${userId} in hub ${hubId}: ${amount > 0 ? '+' : ''}${amount} RP (${options.reason})`,
      );

      const duration = performance.now() - startTime;
      Logger.debug(`Reputation change processed in ${duration}ms`);

      return updatedReputation.reputation;
    }
    catch (error) {
      Logger.error('Error changing reputation:', error);
      throw error;
    }
  }

  /**
   * Gets a user's reputation in a specific hub
   * @param userId - The user ID
   * @param hubId - The hub ID
   * @returns The user's reputation in the hub
   */
  async getUserReputation(userId: Snowflake, hubId: string): Promise<number> {
    const cacheKey = `${userId}:${hubId}`;

    return (
      (await this.cacheManager.get(cacheKey, async () => {
        const reputation = await db.hubReputation.findUnique({
          where: {
            userId_hubId: { userId, hubId },
          },
          select: {
            reputation: true,
          },
        });

        return reputation?.reputation ?? 0;
      })) ?? 0
    );
  }

  /**
   * Gets reputation requirements for a specific hub
   * @param hubId - The hub ID
   * @returns The reputation requirements for various features
   */
  async getHubReputationRequirements(hubId: string): Promise<ReputationRequirements> {
    const settings = await this.getHubReputationSettings(hubId);

    return {
      imageUpload: settings.imageUploadRequirement,
      gifUpload: settings.gifUploadRequirement,
      videoUpload: settings.videoUploadRequirement,
      sticker: settings.stickerRequirement,
      longMessage: settings.longMessageRequirement,
      embed: settings.embedRequirement,
    };
  }

  /**
   * Checks if a user meets the reputation requirement for a specific feature
   * @param userId - The user ID
   * @param hubId - The hub ID
   * @param feature - The feature to check
   * @returns Whether the user meets the requirement
   */
  async checkReputationRequirement(
    userId: Snowflake,
    hubId: string,
    feature: keyof ReputationRequirements,
  ): Promise<boolean> {
    const [userReputation, requirements] = await Promise.all([
      this.getUserReputation(userId, hubId),
      this.getHubReputationRequirements(hubId),
    ]);

    return userReputation >= requirements[feature];
  }

  /**
   * Gets hub reputation settings with caching
   * @param hubId - The hub ID
   * @returns The hub's reputation settings
   */
  async getHubReputationSettings(hubId: string): Promise<HubReputationSettings> {
    const cached = await this.settingsCacheManager.get(hubId, async () => {
      const settings = await db.hubReputationSettings.findUnique({
        where: { hubId },
      });

      // Return default settings if none exist
      if (!settings) {
        return await this.createDefaultReputationSettings(hubId);
      }

      return settings;
    });

    return cached as unknown as HubReputationSettings;
  }

  /**
   * Creates default reputation settings for a hub
   * @param hubId - The hub ID
   * @returns The created settings
   */
  private async createDefaultReputationSettings(hubId: string): Promise<HubReputationSettings> {
    return await db.hubReputationSettings.create({
      data: { hubId },
    });
  }

  /**
   * Invalidates cache for a specific user-hub reputation
   * @param userId - The user ID
   * @param hubId - The hub ID
   */
  async invalidateUserReputationCache(userId: Snowflake, hubId: string): Promise<void> {
    const cacheKey = `${userId}:${hubId}`;
    await this.cacheManager.delete(cacheKey);
  }

  /**
   * Invalidates settings cache for a hub
   * @param hubId - The hub ID
   */
  async invalidateHubSettingsCache(hubId: string): Promise<void> {
    await this.settingsCacheManager.delete(hubId);
  }

  /**
   * Updates reputation settings for a hub
   * @param hubId - The hub ID
   * @param settings - Partial settings to update
   * @returns The updated settings
   */
  async updateHubReputationSettings(
    hubId: string,
    settings: Partial<Omit<HubReputationSettings, 'id' | 'hubId' | 'createdAt' | 'updatedAt'>>,
  ): Promise<HubReputationSettings> {
    const updatedSettings = await db.hubReputationSettings.upsert({
      where: { hubId },
      create: {
        hubId,
        ...settings,
      },
      update: settings,
    });

    // Invalidate cache
    await this.invalidateHubSettingsCache(hubId);

    return updatedSettings;
  }

  /**
   * Gets top users by reputation in a hub
   * @param hubId - The hub ID
   * @param limit - Number of users to return
   * @returns Array of users with their reputation
   */
  async getTopReputationUsers(
    hubId: string,
    limit: number = 10,
  ): Promise<Array<{ userId: string; reputation: number }>> {
    return await db.hubReputation.findMany({
      where: {
        hubId,
        reputation: { gt: 0 },
      },
      select: {
        userId: true,
        reputation: true,
      },
      orderBy: {
        reputation: 'desc',
      },
      take: limit,
    });
  }

  /**
   * Gets all reputation data for a user across all hubs
   * @param userId - The user ID
   * @returns Array of hub reputations for the user
   */
  async getUserReputationAcrossHubs(
    userId: Snowflake,
  ): Promise<(HubReputation & { hub: { id: string; name: string; iconUrl: string | null } })[]> {
    return await db.hubReputation.findMany({
      where: {
        userId,
        reputation: { gt: 0 },
      },
      include: {
        hub: {
          select: {
            id: true,
            name: true,
            iconUrl: true,
          },
        },
      },
      orderBy: {
        reputation: 'desc',
      },
    });
  }

  /**
   * Bulk invalidates reputation cache for all users in a hub
   * Used when hub settings change that might affect reputation calculations
   * @param hubId - The hub ID
   */
  async invalidateHubReputationCache(hubId: string): Promise<void> {
    const redis = getRedis();
    const pattern = `${this.cacheManager.redis.options.keyPrefix || ''}${RedisKeys.HubReputation}:*:${hubId}`;

    // Use SCAN to find and delete all matching keys
    const stream = redis.scanStream({
      match: pattern,
      count: 100,
    });

    const keysToDelete: string[] = [];

    stream.on('data', (keys: string[]) => {
      keysToDelete.push(...keys);
    });

    stream.on('end', async () => {
      if (keysToDelete.length > 0) {
        await redis.del(...keysToDelete);
        Logger.debug(
          `Invalidated ${keysToDelete.length} reputation cache entries for hub ${hubId}`,
        );
      }
    });
  }
}
